import asyncio
import base64
import os
import re
import tempfile
import uuid
from typing import Any, List

import cv2
import httpx
import openai
from asyncio_throttle import Throttler
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# Directory to save media
download_dir = "tweet_media"
os.makedirs(download_dir, exist_ok=True)

# Get API keys from environment variables
OPENAI_API_KEY = os.getenv(
    "OPENAI_API_KEY", "********************************************************"
)
bearer_token = "AAAAAAAAAAAAAAAAAAAAAIvGrQEAAAAAjPcTuuFSWhK%2Fvjd8B5L%2BdYv9WkE%3DyabUj9jDk3HE9tS5bbbwsreuH66TpyGKBqP71XQFlO7r8NdDAK"  # Replace with env var ideally

router = APIRouter()

# Create more permissive throttlers for different operations
media_throttler = Throttler(
    rate_limit=60, period=60
)  # 60 media downloads per 60 seconds (increased from 30)
openai_throttler = Throttler(
    rate_limit=40, period=60
)  # 40 OpenAI calls per 60 seconds (increased from 20)
tiktok_throttler = Throttler(
    rate_limit=2, period=5
)  # 2 TikTok API calls per 5 seconds (kept same as requested)

# Global async OpenAI client pool for better performance
_openai_client_pool = []
_openai_client_lock = asyncio.Lock()
_openai_client_index = 0


async def get_openai_client():
    """Get or create async OpenAI client with improved concurrency."""
    global _openai_client_pool, _openai_client_index

    # Quick check without lock for performance
    if _openai_client_pool:
        # Round-robin selection for load balancing
        client = _openai_client_pool[_openai_client_index % len(_openai_client_pool)]
        _openai_client_index += 1
        return client

    # Initialize clients if not already done
    async with _openai_client_lock:
        if not _openai_client_pool:
            if not OPENAI_API_KEY:
                raise ValueError("OPENAI_API_KEY not found")

            # Create multiple OpenAI clients for better concurrency
            for _ in range(2):  # Create 2 OpenAI clients
                client = openai.AsyncOpenAI(
                    api_key=OPENAI_API_KEY,
                    max_retries=3,
                    timeout=30.0,
                )
                _openai_client_pool.append(client)

        # Return first client after initialization
        return _openai_client_pool[0]


# Global HTTP client pool for better concurrency
_http_client_pool = []
_http_client_lock = asyncio.Lock()
_http_client_index = 0


async def get_http_client():
    """Get or create async HTTP client with improved concurrency settings."""
    global _http_client_pool, _http_client_index

    async with _http_client_lock:
        if not _http_client_pool:
            # Create multiple HTTP clients for better concurrency
            for _ in range(3):  # Create 3 HTTP clients
                client = httpx.AsyncClient(
                    timeout=httpx.Timeout(30.0),
                    limits=httpx.Limits(
                        max_keepalive_connections=20,  # Increased from 5
                        max_connections=50,  # Increased from 10
                        keepalive_expiry=30.0,  # Keep connections alive longer
                    ),
                    http2=True,  # Enable HTTP/2 for better multiplexing
                )
                _http_client_pool.append(client)

        # Round-robin selection for load balancing
        client = _http_client_pool[_http_client_index % len(_http_client_pool)]
        _http_client_index += 1
        return client


# Define common media extensions (lowercase)
VIDEO_EXTENSIONS = {".mp4", ".mov", ".avi", ".mkv", ".wmv", ".flv", ".webm"}
IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"}


def get_base_messages():
    """Get a fresh copy of base messages for each request to avoid shared state."""
    return [
        {
            "role": "system",
            "content": """
               # NYX Pro Makeup US Customer Service AI Agent

## Role & Identity
You are a professional beauty advisor for NYX Professional Makeup US, mirroring the brand's multi-channel customer service approach. You combine the expertise of NYX's beauty advisor pros with the accessibility of their live chat support. You specialize in personalized product recommendations, expert makeup tips, and comprehensive support for NYX's cruelty-free, affordable professional makeup line.

## Core Guidelines

### Scope Limitations
- **ONLY respond to NYX Pro Makeup US related questions, product recommendations, and inquiries**
- If asked about competitors, other brands, or unrelated topics, politely redirect to NYX products
- Focus exclusively on products available in the US market

### Response Principles
- **Speak the Language**: Use current beauty slang, TikTok terminology, and community phrases
- **Be Authentically Enthusiastic**: Match the energy of beauty content creators and influencers
- **Confidence Over Politeness**: Be bold about product recommendations - "This IS the one"
- **Community Insider Vibes**: Talk like you're sharing secrets with beauty besties
- **Trend Awareness**: Reference current beauty trends, techniques, and cultural moments
- **Unapologetic Quality Claims**: Don't hedge - if NYX does something well, say it with conviction

## Key Responsibilities

### 1. Personalized Product Recommendations
- Act like NYX's personalized foundation finder - ask targeted questions
- Suggest products based on skin tone, skin type, occasion, or desired look
- Emphasize professional quality at affordable prices
- Highlight cruelty-free formulations and high color payoff
- Recommend complementary products for complete, professional looks
- Consider customer's experience level and provide scalable advice

### 2. Product Information
- Provide details about ingredients, formulation, and coverage
- Explain product differences and help customers choose between similar items
- Share shade matching guidance
- Discuss longevity, finish, and performance characteristics

### 3. Application Guidance
- Offer step-by-step application tips
- Suggest appropriate tools and brushes
- Provide techniques for different skill levels
- Share tips for product longevity and best results

### 4. Troubleshooting
- Help solve common makeup application issues
- Suggest alternative products if current ones aren't working
- Provide tips for specific concerns (oily skin, mature skin, sensitive eyes, etc.)

## Response Format

### For Product Recommendations:
1. **Ask clarifying questions** if needed (skin tone, preferences, occasion)
2. **Suggest 2-3 specific products** with names and brief descriptions
3. **Explain why** each product suits their needs
4. **Include application tips** when relevant
5. **Suggest complementary products** if appropriate

### For General Questions:
1. **Acknowledge** their question enthusiastically
2. **Provide specific information** about NYX products/techniques
3. **Offer additional tips** or related suggestions
4. **Ask if they need more help** with anything else

## Example Interactions

**Customer**: "I need help finding a good foundation for oily skin"

**Response Structure**:
- "bestie, you came to the RIGHT place 🙌"
- Ask about their skin tone with enthusiasm: "okay but first - what's your undertone situation?"
- Recommend 2-3 NYX foundations with bold confidence: "The Can't Stop Won't Stop foundation? It's literally THAT girl for oily skin"
- Explain benefits using trendy language: "The coverage is giving full glam but feels like nothing"
- Suggest complementary products: "And don't sleep on the Angel Veil primer - it's the perfect base bestie"
- End with confidence: "Trust me, this combo will have your base looking snatched all day"

## Boundaries & Redirects

### When Asked About Non-NYX Topics:
"bestie, I'm all about that NYX life 💅 But honestly? NYX has exactly what you're looking for. Let me put you onto some products that are about to change your whole routine..."

### When Asked About Competitors:
"Listen, I don't need to talk about other brands when NYX is literally serving LOOKS at these prices. The quality? Unmatched. The shade range? Chef's kiss. Let me show you why NYX girls just hit different..."

### When Asked Unrelated Questions:
"okay but can we talk about your makeup routine though? 👀 I'm here for all things NYX and I just KNOW there's something in the collection that's going to become your new obsession..."

## Tone & Voice
- **Social Media Savvy**: Use trendy, authentic language that resonates with beauty communities
- **Confident & Knowing**: "The girls that get it, GET it" energy - speak to those who understand quality
- **Gen Z/Millennial Friendly**: Use current slang, beauty terminology, and cultural references naturally
- **Unapologetically Enthusiastic**: Bold statements about product quality and performance
- **Community-Focused**: Speak like you're part of the beauty community, not just selling to it
- **Professional but Relatable**: Expert knowledge delivered with personality and authenticity

## Key Phrases to Use
- "Period. The girls that get it, GET it 💅"
- "This is IT. This is THE one."
- "No literally, this foundation is unmatched"
- "Trust the process - NYX never misses"
- "The way this [product] just hits different..."
- "bestie, you NEED this in your collection"
- "It's giving professional MUA vibes for drugstore prices"
- "The color payoff? Absolutely sending me"
- "This formula understood the assignment"
- "Not me gatekeeping this shade... actually yes I am"
- "The grip this [product] has on the beauty community..."
- "It's the [product feature] for me"

## Always Remember
- Stay within NYX Professional Makeup US scope
- Prioritize customer satisfaction and product suitability
- Build confidence in customers' makeup abilities
- Promote the quality and innovation of NYX products
- Encourage experimentation and creativity with makeup
""",
        },
    ]


class TweetDetailsPayload(BaseModel):
    tweetData: Any


class TikTokPayload(BaseModel):
    video_url: Any


class InstagramPayload(BaseModel):
    path: str
    coauthor_producers: Any
    edge_media_to_caption: Any


class InstagramPayloadPhoto(BaseModel):
    photo_data: Any


class AnalysisResponse(BaseModel):
    response: List[str]


def remove_links(text):
    return re.sub(r"https?://\S+", "", text).strip()


@router.post("/generate_response")
async def generate_response(payload: TweetDetailsPayload):
    tweet = payload.tweetData
    clean_text = remove_links(tweet["fullText"])
    if not tweet["id"]:
        print("Error: Missing 'tweet' (ID) in request body.")
        return JSONResponse(
            status_code=400, content={"message": "Missing 'tweet' (ID) in request body"}
        )

    # Initialize variables for categorization
    video_files = []
    image_files = []
    other_files = []  # Optional: to catch unexpected file types

    try:
        # Use per-request message copy and OpenAI client
        tweet_messages = get_base_messages()
        tweet_messages.append({"role": "user", "content": clean_text})

        # Create per-request OpenAI client
        client = await get_openai_client()

        # Get HTTP client from pool for media downloads
        http_client = await get_http_client()

        if tweet.get("media", None):
            for file_path in tweet["media"]:
                try:
                    _, extension = os.path.splitext(file_path["url"])
                    extension_lower = extension.lower()
                    print(extension_lower)
                    print(file_path["url"])

                    if extension_lower in VIDEO_EXTENSIONS:
                        video_files.append(file_path["url"])
                    elif extension_lower in IMAGE_EXTENSIONS:
                        image_files.append(file_path["url"])
                    else:
                        if extension:
                            other_files.append(file_path["url"])
                        print(f"Note: Uncategorized media type: {file_path}")

                except Exception as e:
                    print(
                        f"Warning: Could not process media file path '{file_path}': {e}"
                    )

        print(f"Categorized Videos: {video_files}")
        print(f"Categorized Images: {image_files}")
        if other_files:
            print(f"Other Media Types: {other_files}")

        # Process videos with async HTTP calls and throttling
        for media in video_files:
            async with media_throttler:
                response = await http_client.get(media)
                response.raise_for_status()

                # Save video content to temporary file for cv2 processing
                with tempfile.NamedTemporaryFile(
                    delete=False, suffix=".mp4"
                ) as tmp_file:
                    tmp_file.write(response.content)
                    tmp_path = tmp_file.name

                try:
                    video = cv2.VideoCapture(tmp_path)
                    base64Frames = []
                    frame_count = 0
                    while (
                        video.isOpened() and frame_count < 1000
                    ):  # Limit frames to prevent memory issues
                        success, frame = video.read()
                        if not success:
                            break
                        if frame_count % 100 == 0:  # Sample every 100th frame
                            _, buffer = cv2.imencode(".jpg", frame)
                            base64Frames.append(
                                base64.b64encode(buffer).decode("utf-8")
                            )
                        frame_count += 1
                    video.release()

                    if base64Frames:
                        tweet_messages.append(
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": f"data:image/jpeg;base64,{x}"
                                        },
                                    }
                                    for x in base64Frames[:10]  # Limit to 10 frames max
                                ],
                            }
                        )
                finally:
                    # Clean up temporary file
                    try:
                        os.unlink(tmp_path)
                    except OSError:
                        pass

        # Process images with async HTTP calls and throttling
        for media in image_files:
            async with media_throttler:
                response = await http_client.get(media)
                response.raise_for_status()
                base64_image = base64.b64encode(response.content).decode("utf-8")
                tweet_messages.append(
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                },
                            },
                        ],
                    }
                )

        # Make OpenAI API call with throttling
        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                messages=tweet_messages,
                max_tokens=128,
                n=3,
            )
        print("response_completion done")
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

            response_content_model = AnalysisResponse(response=analysis_results_list)
            return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        # Catch errors during processing
        print(f"Error processing tweet {tweet['id']}: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500, content={"message": f"Error processing tweet: {str(e)}"}
        )


ms_token = os.getenv(
    "TIKTOK_MS_TOKEN",
    "tbNLOJaUy7_vBktSLptcACRHmjQNURMWzaWZ-NQPoQsMgYhP6-U0Ca8xIKtKQJ5glmJ_DJr9Epgu_lsJgJY9LAWOhhYftWSR8eVLObKrAK4fyw_76fsXS4OtCuT54ST4iQx_S-ltZYRaAQ==",
)
from TikTokApi import TikTokApi

# TikTok API session pool for better concurrency
_tiktok_api_pool = []
_tiktok_api_lock = asyncio.Lock()
_session_index = 0


async def get_tiktok_api():
    """Get or create TikTok API instance with proper session management."""
    global _tiktok_api_pool, _session_index
    async with _tiktok_api_lock:
        if not _tiktok_api_pool:
            # Create multiple API instances for better concurrency
            for _ in range(1):
                api = TikTokApi()
                await api.create_sessions(
                    ms_tokens=[ms_token],
                    num_sessions=1,  # 2 sessions per API instance
                    sleep_after=3,  # Reduced sleep time
                    browser=os.getenv("TIKTOK_BROWSER", "chromium"),
                )
                _tiktok_api_pool.append(api)

        # Round-robin selection for load balancing
        api = _tiktok_api_pool[_session_index % len(_tiktok_api_pool)]
        _session_index += 1
        return api


@router.post("/generate_tiktok_response")
async def generate_response_tiktok(video_url: TikTokPayload):
    try:
        # Get shared TikTok API instance
        api = await get_tiktok_api()

        # Create per-request OpenAI client and message copy
        client = await get_openai_client()
        tiktok_messages = get_base_messages()

        # Get video info with throttling
        async with tiktok_throttler:
            video_info = await api.video(url=video_url.video_url).info()

        tiktok_messages.append({"role": "user", "content": video_info["desc"]})

        # Get session and download video
        _, session = api._get_session()
        downloadAddr = video_info["video"]["downloadAddr"]
        cookies = await api.get_session_cookies(session)

        # Get HTTP client from pool for video download
        http_client = await get_http_client()

        headers = {
            "range": "bytes=0-",
            "accept-encoding": "identity;q=1, *;q=0",
            "referer": "https://www.tiktok.com/",
            **dict(session.headers),
        }

        async with tiktok_throttler:
            resp = await http_client.get(downloadAddr, headers=headers, cookies=cookies)
            resp.raise_for_status()

            # Save video to temporary file with unique name
            unique_id = str(uuid.uuid4())
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=f"_{unique_id}.mp4"
            ) as tmpf:
                tmpf.write(resp.content)
                tmp_path = tmpf.name

            try:
                # Process video frames
                video = cv2.VideoCapture(tmp_path)
                base64Frames = []
                frame_count = 0

                while video.isOpened() and frame_count < 1000:  # Limit frames
                    success, frame = video.read()
                    if not success:
                        break
                    if frame_count % 100 == 0:  # Sample every 100th frame
                        _, buffer = cv2.imencode(".jpg", frame)
                        base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
                    frame_count += 1

                video.release()

                if base64Frames:
                    tiktok_messages.append(
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/jpeg;base64,{x}"},
                                }
                                for x in base64Frames[:10]  # Limit to 10 frames
                            ],
                        }
                    )
            finally:
                # Clean up temporary file
                try:
                    os.unlink(tmp_path)
                except OSError:
                    pass

        # Make OpenAI API call with throttling
        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                messages=tiktok_messages,
                max_tokens=128,
                n=3,
            )
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing TikTok video: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing TikTok video: {str(e)}"},
        )


@router.post("/generate_instagram_response_video")
async def generate_response_instagram(payload: InstagramPayload):
    print(payload)
    try:
        path = payload.path
        print(f"Received path: {path}")

        # Create per-request OpenAI client and message copy
        client = await get_openai_client()
        instagram_messages = get_base_messages()

        # Debug: List files in /tmp directory
        print("Files in /tmp directory:")
        try:
            for file in os.listdir("/tmp"):
                file_path = os.path.join("/tmp", file)
                file_size = (
                    os.path.getsize(file_path)
                    if os.path.isfile(file_path)
                    else "directory"
                )
                print(f"  - {file} ({file_size})")
        except Exception as e:
            print(f"Error listing /tmp: {e}")

        if not os.path.exists(path):
            # Try to handle Docker volume path mapping
            print(f"Path not found: {path}")

            # Try alternative paths
            alt_paths = [path, path.replace("/tmp/", "/app/tmp/"), f"/app{path}"]

            for alt_path in alt_paths:
                print(f"Trying alternative path: {alt_path}")
                if os.path.exists(alt_path):
                    print(f"Found file at: {alt_path}")
                    path = alt_path
                    break
            else:
                return JSONResponse(
                    status_code=400,
                    content={
                        "message": f"File not found at path: {path}. Make sure volumes are properly mounted between services."
                    },
                )

        # Process video with frame limiting
        video = cv2.VideoCapture(path)
        base64Frames = []
        frame_count = 0

        while video.isOpened() and frame_count < 1000:  # Limit frames
            success, frame = video.read()
            if not success:
                break
            if frame_count % 10 == 0:  # Sample every 100th frame
                _, buffer = cv2.imencode(".jpg", frame)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
            frame_count += 1
        video.release()

        if base64Frames:
            instagram_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{x}"},
                        }
                        for x in base64Frames[:10]  # Limit to 10 frames
                    ],
                }
            )

        # Make OpenAI API call with throttling
        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                messages=instagram_messages,
                max_tokens=128,
                n=3,
            )
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing Instagram video: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing Instagram video: {str(e)}"},
        )


@router.post("/generate_instagram_response_photo")
async def generate_response_instagram_photo(photo_data: InstagramPayloadPhoto):
    try:
        # Create per-request OpenAI client and message copy
        client = await get_openai_client()
        instagram_messages = get_base_messages()

        data = photo_data.photo_data["data"]["xdt_shortcode_media"]
        post_text = data["edge_media_to_caption"]["edges"][0]["node"]["text"]
        instagram_messages.append({"role": "user", "content": post_text})

        image_url = data["display_url"]

        # Get HTTP client from pool for image download
        http_client = await get_http_client()

        async with media_throttler:
            response = await http_client.get(image_url)
            response.raise_for_status()
            base64_image = base64.b64encode(response.content).decode("utf-8")
            instagram_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            },
                        },
                    ],
                }
            )

        # Make OpenAI API call with throttling
        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                messages=instagram_messages,
                max_tokens=128,
                n=3,
            )

        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing Instagram photo: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing Instagram photo: {str(e)}"},
        )
